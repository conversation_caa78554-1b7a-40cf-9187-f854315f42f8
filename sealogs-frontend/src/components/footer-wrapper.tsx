'use client'

import { cn } from '@/app/lib/utils'
import type * as React from 'react'

interface FooterWrapperProps extends React.HTMLAttributes<HTMLDivElement> {
    noBorder?: boolean
    bottom?: string
    parentClassName?: string
    children: React.ReactNode
}

export function FooterWrapper({
    noBorder = false,
    bottom,
    parentClassName,
    children,
    className,
    ...props
}: FooterWrapperProps) {
    return (
        <>
            <div className="h-6" />
            <div
                className={cn(
                    ' absolute w-full z-50 bottom-0 inset-x-0 px-2.5 pb-[10px]',
                    parentClassName,
                )}
                {...props}>
                <div
                    className={cn(
                        'flex w-full justify-end gap-2.5 md:gap-3 px-2.5 text-wedgewood-50 border-border border bg-curious-blue-950 rounded-md shadow-md py-2',
                        'md:px-3',
                        className,
                    )}>
                    {children}
                </div>
            </div>
        </>
    )
}
