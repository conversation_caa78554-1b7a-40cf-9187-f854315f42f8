'use client'
import React, { useState, useEffect, useRef } from 'react'
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from '@/components/ui/popover'
import { AlertDialogNew } from '@/components/ui/alert-dialog-new'
import { Combobox } from '@/components/ui/comboBox'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import {
    AlertCircle,
    InfoIcon,
    MapPin,
    Move3d,
    Navigation,
    Save,
    X,
} from 'lucide-react'
import { Separator } from '@/components/ui/separator'
import { useLazyQuery, useMutation } from '@apollo/client'
import { toast } from 'sonner'
import { P } from '@/components/ui/typography'
// Sheet components are not needed in this file
import {
    CREATE_GEO_LOCATION,
    CreateFavoriteLocation,
    UpdateTripReport_LogBookEntrySection,
} from '@/app/lib/graphQL/mutation'
import { GetFavoriteLocations } from '@/app/lib/graphQL/query'
import FavoriteLocationModel from '@/app/offline/models/favoriteLocation'
import TripReport_LogBookEntrySectionModel from '@/app/offline/models/tripReport_LogBookEntrySection'
import GeoLocationModel from '@/app/offline/models/geoLocation'
import { generateUniqueId } from '@/app/offline/helpers/functions'

type LocationSelectorProps = {
    geoLocations: any
    currentTrip: any
    tripReport: any
    updateTripReport: (params: any) => void
    // New optional callback – when current coordinates are set or updated
    setCurrentLocation?: (location: {
        latitude: number
        longitude: number
    }) => void
    offline?: boolean
    label: string // e.g., "Depart Location" or "Expected Location"
    locationField: 'from' | 'to' // determines which fields to update
    disabled?: boolean
}

export default function LocationSelector({
    geoLocations = false,
    currentTrip,
    tripReport,
    updateTripReport,
    setCurrentLocation, // new optional callback prop
    offline = false,
    label,
    locationField,
    disabled = false,
}: LocationSelectorProps) {
    // Determine the field keys based on the location type
    const locationIDKey =
        locationField === 'from' ? 'fromLocationID' : 'toLocationID'
    const latKey = locationField === 'from' ? 'fromLat' : 'toLat'
    const longKey = locationField === 'from' ? 'fromLong' : 'toLong'

    const latInputRef = useRef<HTMLInputElement | null>(null)
    const longInputRef = useRef<HTMLInputElement | null>(null)

    const favoriteLocationModel = new FavoriteLocationModel()
    const tripReportModel = new TripReport_LogBookEntrySectionModel()
    const geoLocationModel = new GeoLocationModel()

    const [showLocation, setShowLocation] = useState(false)
    const [newLocation, setNewLocation] = useState(false)
    const [closestLocation, setClosestLocation] = useState<any>(false)
    const [selectedLocation, setSelectedLocation] = useState<any>(false)
    const [favoriteLocations, setFavoriteLocations] = useState<any>([])
    const [selectedParentLocation, setSelectedParentLocation] =
        useState<any>(false)
    const [locations, setLocations] = useState<any>([])
    const [openNewLocationDialog, setOpenNewLocationDialog] =
        useState<boolean>(false)
    const [openSetLocationDialog, setOpenSetLocationDialog] =
        useState<boolean>(false)

    const [location, setLocation] = useState<{
        latitude: string
        longitude: string
    }>({
        latitude: '',
        longitude: '',
    })

    // Utility to sort locations by favorite usage
    const sortLocations = (locs: any[]) => {
        if (favoriteLocations.length > 0) {
            locs.sort((a: any, b: any) => {
                const aFav = favoriteLocations.find(
                    (fav: any) => fav.geoLocationID === a.value,
                )
                const bFav = favoriteLocations.find(
                    (fav: any) => fav.geoLocationID === b.value,
                )
                if (aFav && bFav) {
                    return bFav.usage - aFav.usage
                } else if (aFav) {
                    return -1
                } else if (bFav) {
                    return 1
                }
                return 0
            })
        }
        return locs
    }

    const allLocations = () => {
        let locArray = geoLocations
            ? geoLocations.map((loc: any) => ({
                  label: loc.title,
                  value: loc.id,
                  latitude: loc.lat,
                  longitude: loc.long,
              }))
            : []
        if (locations && locations.length > 0) {
            locArray = [...locations, ...locArray]
        }
        return sortLocations(locArray)
    }

    // Offline favorite locations
    const getOfflineFavoriteLocations = async () => {
        if (selectedLocation) {
            await tripReportModel.save({
                id: currentTrip.id,
                [locationIDKey]: selectedLocation.value,
            })
            const userId = localStorage.getItem('userId')
            if (userId && +userId > 0) {
                await favoriteLocationModel.save({
                    id: generateUniqueId(),
                    memberID: +userId,
                    geoLocationID: +selectedLocation.value,
                })
            }
            const favs = await favoriteLocationModel.getByMemberID(
                localStorage.getItem('userId')!,
            )
            setFavoriteLocations(favs)
        }
    }

    // Fetch favorite locations (online)
    const [getFavoriteLocations] = useLazyQuery(GetFavoriteLocations, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response) => {
            if (selectedLocation && +selectedLocation.value > 0) {
                updateTripReport({
                    id: currentTrip.id,
                    key: locationIDKey,
                    value: selectedLocation.value,
                })
                const userId = localStorage.getItem('userId')
                if (userId && +userId > 0) {
                    createFavoriteLocation({
                        variables: {
                            input: {
                                memberID: +userId,
                                geoLocationID: +selectedLocation.value,
                            },
                        },
                    })
                }
            }
        },
        onError: (error) => {
            console.error('Error fetching favorite locations', error)
        },
    })

    useEffect(() => {
        if (offline) {
            getOfflineFavoriteLocations()
        } else {
            getFavoriteLocations({
                variables: { userID: +localStorage.getItem('userId')! },
            })
        }
    }, [])

    // Determine the current selected location from tripReport (if available)
    const getSelectedLocation = () => {
        if (selectedLocation) return selectedLocation
        if (tripReport) {
            const trip = tripReport.find(
                (trip: any) => trip.id === currentTrip.id,
            )
            const locData =
                locationField === 'from' ? trip?.fromLocation : trip?.toLocation
            if (locData?.id > 0) {
                return {
                    label: locData.title,
                    value: locData.id,
                    latitude: locData.lat,
                    longitude: locData.long,
                }
            }
        }
        return selectedLocation
    }

    useEffect(() => {
        if (tripReport) {
            setSelectedLocation(false)
            const trip = tripReport.find(
                (trip: any) => trip.id === currentTrip.id,
            )
            const locData =
                locationField === 'from' ? trip?.fromLocation : trip?.toLocation
            const coordLat =
                locationField === 'from' ? trip?.fromLat : trip?.toLat
            const coordLong =
                locationField === 'from' ? trip?.fromLong : trip?.toLong
            if (locData?.id > 0) {
                setSelectedLocation({
                    label: locData.title,
                    value: locData.id,
                    latitude: locData.lat,
                    longitude: locData.long,
                })
                setLocation({ latitude: locData.lat, longitude: locData.long })
                setShowLocation(false)
            } else if (coordLat !== 0 && coordLong !== 0) {
                setLocation({ latitude: coordLat, longitude: coordLong })
                setShowLocation(true)
                // If a setCurrentLocation callback is provided, call it here
                if (setCurrentLocation) {
                    setCurrentLocation({
                        latitude: +coordLat,
                        longitude: +coordLong,
                    })
                }
            }
        }
    }, [tripReport])

    // Handle location change via the combobox
    const handleLocationChange = async (option: any) => {
        if (option.value === 'newLocation') {
            setSelectedParentLocation(false)
            setOpenNewLocationDialog(true)
        } else {
            setSelectedLocation(option)
            if (offline) {
                await tripReportModel.save({
                    id: currentTrip.id,
                    [locationIDKey]: option.value,
                })
                updateTripReport({
                    id: currentTrip.id,
                    key: locationIDKey,
                    value: option.value,
                })
            } else {
                updateTripReport_LogBookEntrySectionLocationID({
                    variables: {
                        input: {
                            id: currentTrip.id,
                            [locationIDKey]: option.value,
                        },
                    },
                })
            }
            const userId = localStorage.getItem('userId')
            if (userId && +userId > 0) {
                if (offline) {
                    await favoriteLocationModel.save({
                        id: generateUniqueId(),
                        memberID: +userId,
                        geoLocationID: +option.value,
                    })
                    const favs = await favoriteLocationModel.getByMemberID(
                        localStorage.getItem('userId')!,
                    )
                    setFavoriteLocations(favs)
                } else {
                    createFavoriteLocation({
                        variables: {
                            input: {
                                memberID: +userId,
                                geoLocationID: +option.value,
                            },
                        },
                    })
                }
            }
        }
    }

    const [createFavoriteLocation] = useMutation(CreateFavoriteLocation, {
        onError: (error) => {
            console.error('Error creating favorite location', error)
        },
    })

    const handleParentLocationChange = (option: any) => {
        setSelectedParentLocation(option)
    }

    const handleCreateNewLocation = async () => {
        const title = (
            document.getElementById('new-location-title') as HTMLInputElement
        )?.value
        const parentLocation = selectedParentLocation
            ? selectedParentLocation.value
            : null
        const latitude = (
            document.getElementById('new-location-latitude') as HTMLInputElement
        )?.value
        const longitude = (
            document.getElementById(
                'new-location-longitude',
            ) as HTMLInputElement
        )?.value

        const variables = {
            title,
            lat: +latitude,
            long: +longitude,
            parentLocationID: parentLocation,
        }

        if (offline) {
            const data = await geoLocationModel.save({
                ...variables,
                id: generateUniqueId(),
            })
            setLocations((prev: any) => (prev ? [...prev, data] : [data]))
            setSelectedLocation({
                label: data.title,
                value: data.id,
                latitude: data.lat,
                longitude: data.long,
            })
            setOpenNewLocationDialog(false)
            setOpenSetLocationDialog(false)
        } else {
            createGeoLocation({ variables: { input: variables } })
        }
    }

    const [createGeoLocation] = useMutation(CREATE_GEO_LOCATION, {
        onCompleted: (response) => {
            const data = response.createGeoLocation
            setLocations((prev: any) => (prev ? [...prev, data] : [data]))
            setSelectedLocation({
                label: data.title,
                value: data.id,
                latitude: data.lat,
                longitude: data.long,
            })
            setOpenNewLocationDialog(false)
            setOpenSetLocationDialog(false)
        },
        onError: (error) => {
            console.error('Error creating geo location', error)
        },
    })

    const getProximity = (loc: any, lat: number, long: number) => {
        return Math.sqrt(
            Math.pow(loc.lat - lat, 2) + Math.pow(loc.long - long, 2),
        )
    }

    const findClosestLocation = async (lat: number, long: number) => {
        const closest = geoLocations.reduce((prev: any, curr: any) => {
            const prevDist = Math.sqrt(
                Math.pow(prev.lat - lat, 2) + Math.pow(prev.long - long, 2),
            )
            const currDist = Math.sqrt(
                Math.pow(curr.lat - lat, 2) + Math.pow(curr.long - long, 2),
            )
            return prevDist < currDist ? prev : curr
        })
        const proximity = getProximity(closest, lat, long)
        setClosestLocation({
            label: closest.title,
            value: closest.id,
            latitude: closest.lat,
            longitude: closest.long,
        })
        if (proximity > 0.15 || (closest.lat === 0 && closest.long === 0)) {
            toast.error('No location found within 10 KM radius!')
            return
        }
        if (offline) {
            await tripReportModel.save({
                id: currentTrip.id,
                [locationIDKey]: closest.id,
            })
        } else {
            updateTripReport_LogBookEntrySectionLocationID({
                variables: {
                    input: { id: currentTrip.id, [locationIDKey]: closest.id },
                },
            })
        }
    }

    const handleSetCurrentLocation = () => {
        toast.dismiss()
        toast.loading('Getting your current location... Please wait...')
        setOpenSetLocationDialog(true)
        if ('geolocation' in navigator) {
            const options = { timeout: 30000 }
            navigator.geolocation.getCurrentPosition(
                ({ coords }) => {
                    const { latitude, longitude } = coords
                    setLocation({
                        latitude: latitude.toString(),
                        longitude: longitude.toString(),
                    })
                    // Call setCurrentLocation if provided
                    if (setCurrentLocation) {
                        setCurrentLocation({ latitude, longitude })
                    }
                    findClosestLocation(latitude, longitude)
                },
                () => {
                    toast.error('Failed to get current location')
                },
                options,
            )
        } else {
            toast.error('Geolocation is not supported by your browser')
        }
    }

    const [updateTripReport_LogBookEntrySectionLocationID] = useMutation(
        UpdateTripReport_LogBookEntrySection,
        {
            onError: (error) => {
                console.error('Error updating trip report location ID', error)
            },
        },
    )

    const [updateTripReport_LogBookEntrySectionLocation] = useMutation(
        UpdateTripReport_LogBookEntrySection,
        {
            onError: (error) => {
                console.error('Error updating trip report coordinates', error)
            },
        },
    )

    // When coordinates are edited manually, update both trip report and (if provided) notify parent
    const updateLocationCoordinates = async () => {
        const latitude = latInputRef.current?.value ?? ''
        const longitude = longInputRef.current?.value ?? ''
        setLocation({ latitude, longitude })
        if (offline) {
            await tripReportModel.save({
                id: currentTrip.id,
                [latKey]: +latitude,
                [longKey]: +longitude,
                [locationIDKey]: 0,
            })
        } else {
            updateTripReport_LogBookEntrySectionLocation({
                variables: {
                    input: {
                        id: currentTrip.id,
                        [latKey]: +latitude,
                        [longKey]: +longitude,
                        [locationIDKey]: 0,
                    },
                },
            })
        }
        // Also call setCurrentLocation if available
        if (setCurrentLocation) {
            setCurrentLocation({ latitude: +latitude, longitude: +longitude })
        }
    }

    const hideNewLocation = () => {
        setShowLocation(false)
        setNewLocation(false)
    }

    return (
        <Label label={label}>
            <div className={`space-y-4 w-full ${disabled ? 'opacity-50' : ''}`}>
                {geoLocations && !showLocation && (
                    <div className="w-full flex flex-col sm:flex-row gap-2 items-start">
                        <Combobox
                            options={[
                                {
                                    label: 'Add New Location',
                                    value: 'newLocation',
                                },
                                ...allLocations(),
                            ]}
                            value={getSelectedLocation()}
                            onChange={handleLocationChange}
                            placeholder="Select location"
                            buttonClassName="sm:max-w-lg w-full"
                            isDisabled={disabled}
                        />

                        <div className="w-full sm:w-fit flex flex-wrap gap-2 items-center">
                            <Button
                                variant="primaryOutline"
                                onClick={() => setShowLocation(true)}
                                disabled={disabled}
                                className="w-full sm:w-auto"
                                iconLeft={Move3d}>
                                Use Coordinates
                            </Button>
                            <Button
                                variant="primaryOutline"
                                onClick={handleSetCurrentLocation}
                                disabled={disabled}
                                className="w-full sm:w-auto"
                                iconLeft={MapPin}>
                                Current Location
                            </Button>
                            <Popover>
                                <PopoverTrigger asChild>
                                    <Button
                                        variant="ghost"
                                        size="icon"
                                        disabled={disabled}
                                        iconLeft={
                                            <InfoIcon
                                                className="text-light-blue-vivid-900 fill-light-blue-vivid-50"
                                                size={24}
                                            />
                                        }
                                        className="rounded-full"
                                        tooltip="Location information"
                                    />
                                </PopoverTrigger>
                                <PopoverContent className="w-80 p-4">
                                    <div className="space-y-2">
                                        <P className="font-medium">
                                            Location Information
                                        </P>
                                        <P className="text-sm text-muted-foreground">
                                            Automatically sets the nearest
                                            available location based on your GPS
                                            coordinates.
                                        </P>
                                    </div>
                                </PopoverContent>
                            </Popover>
                        </div>
                    </div>
                )}

                {showLocation && (
                    <div className="p-4 max-w-lg border border-dashed border-border bg-background rounded-lg space-y-4">
                        <div className="flex flex-col gap-4">
                            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 flex-1">
                                <Label htmlFor="latitude" label="Latitude">
                                    <Input
                                        id="latitude"
                                        name="latitude"
                                        type="number"
                                        ref={latInputRef}
                                        value={location.latitude}
                                        onBlur={updateLocationCoordinates}
                                        onChange={(e) =>
                                            setLocation({
                                                ...location,
                                                latitude: e.target.value,
                                            })
                                        }
                                        placeholder="Enter latitude"
                                        required
                                        disabled={disabled}
                                    />
                                </Label>

                                <Label htmlFor="longitude" label="Longitude">
                                    <Input
                                        id="longitude"
                                        name="longitude"
                                        type="number"
                                        ref={longInputRef}
                                        value={location.longitude}
                                        onBlur={updateLocationCoordinates}
                                        onChange={(e) =>
                                            setLocation({
                                                ...location,
                                                longitude: e.target.value,
                                            })
                                        }
                                        placeholder="Enter longitude"
                                        required
                                        disabled={disabled}
                                    />
                                </Label>
                            </div>
                            <div className="flex justify-between gap-3">
                                <P className="text-sm text-gray-500 italic">
                                    Enter coordinates in decimal degrees format
                                    <br />
                                    (e.g., 37.80255, -122.41463)
                                </P>
                                <Button
                                    onClick={hideNewLocation}
                                    iconLeft={MapPin}
                                    disabled={disabled}>
                                    Use Location
                                </Button>
                            </div>
                        </div>
                    </div>
                )}

                {newLocation && (
                    <div className="p-6 border rounded-lg bg-gray-50 space-y-5">
                        <div className="flex items-center gap-2 border-b pb-3">
                            <MapPin className="h-5 w-5 text-primary" />
                            <P className="text-xl font-semibold">
                                Add New Location
                            </P>
                        </div>
                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-5">
                            <Label htmlFor="title" label="Location Name">
                                <Input
                                    id="title"
                                    name="title"
                                    type="text"
                                    placeholder="Enter location name"
                                    required
                                    disabled={disabled}
                                />
                            </Label>

                            <Label htmlFor="shortcode" label="Shortcode">
                                <Input
                                    id="shortcode"
                                    name="shortcode"
                                    type="text"
                                    placeholder="Enter shortcode"
                                    required
                                    disabled={disabled}
                                />
                            </Label>

                            <Label
                                htmlFor="parent-location"
                                label="Parent Location">
                                <Combobox
                                    id="parent-location"
                                    options={geoLocations ? allLocations() : []}
                                    onChange={handleParentLocationChange}
                                    placeholder="Enter parent location"
                                    isDisabled={disabled}
                                />
                            </Label>

                            <Label htmlFor="latitude" label="Latitude">
                                <Input
                                    id="latitude"
                                    name="latitude"
                                    type="text"
                                    defaultValue={location.latitude}
                                    placeholder="Enter latitude"
                                    required
                                    disabled={disabled}
                                />
                            </Label>

                            <Label htmlFor="longitude" label="Longitude">
                                <Input
                                    id="longitude"
                                    name="longitude"
                                    type="text"
                                    defaultValue={location.longitude}
                                    placeholder="Enter longitude"
                                    required
                                    disabled={disabled}
                                />
                            </Label>

                            <Label htmlFor="sort-order" label="Sort Order">
                                <Input
                                    id="sort-order"
                                    name="sort-order"
                                    type="text"
                                    placeholder="Enter sort order"
                                    required
                                    disabled={disabled}
                                />
                            </Label>
                        </div>
                        <div className="flex gap-3 pt-3 border-t">
                            <Button
                                onClick={hideNewLocation}
                                disabled={disabled}
                                iconLeft={Save}>
                                Save Location
                            </Button>
                            <Button
                                variant="outline"
                                onClick={hideNewLocation}
                                disabled={disabled}
                                iconLeft={X}>
                                Cancel
                            </Button>
                        </div>
                    </div>
                )}

                {/* New Location Dialog */}
                <AlertDialogNew
                    openDialog={openNewLocationDialog}
                    setOpenDialog={setOpenNewLocationDialog}
                    handleCreate={handleCreateNewLocation}
                    actionText="Save Location"
                    title="Add New Location"
                    contentClassName="sm:max-w-md">
                    <div className="space-y-4 py-2">
                        <Label
                            htmlFor="new-location-title"
                            label="Location Name">
                            <Input
                                id="new-location-title"
                                type="text"
                                placeholder="Enter location name"
                                required
                                disabled={disabled}
                            />
                        </Label>

                        <Label
                            htmlFor="parent-location"
                            label="Parent Location (Optional)">
                            <Combobox
                                id="parent-location"
                                options={geoLocations ? allLocations() : []}
                                onChange={handleParentLocationChange}
                                placeholder="Select parent location"
                                isDisabled={disabled}
                            />
                        </Label>

                        <div className="grid grid-cols-2 gap-4">
                            <Label
                                htmlFor="new-location-latitude"
                                label="Latitude">
                                <Input
                                    id="new-location-latitude"
                                    type="text"
                                    defaultValue={location.latitude}
                                    placeholder="Enter latitude"
                                    required
                                    disabled={disabled}
                                />
                            </Label>

                            <Label
                                htmlFor="new-location-longitude"
                                label="Longitude">
                                <Input
                                    id="new-location-longitude"
                                    type="text"
                                    defaultValue={location.longitude}
                                    placeholder="Enter longitude"
                                    required
                                    disabled={disabled}
                                />
                            </Label>
                        </div>
                    </div>
                </AlertDialogNew>

                {/* Set Location Dialog */}
                <AlertDialogNew
                    openDialog={openSetLocationDialog}
                    setOpenDialog={setOpenSetLocationDialog}
                    handleCreate={handleCreateNewLocation}
                    actionText="Create Location"
                    title="Create Location"
                    contentClassName="sm:max-w-md">
                    {closestLocation?.label ? (
                        <>
                            <div className="bg-gray-50 p-4 rounded-lg mb-4">
                                <P className="text-sm">
                                    Are you in{' '}
                                    <strong className="text-primary">
                                        {closestLocation.label}
                                    </strong>
                                    ?
                                </P>
                                <Button
                                    variant="outline"
                                    onClick={async () => {
                                        // When the user accepts the closest location, update the trip report
                                        if (offline) {
                                            await tripReportModel.save({
                                                id: currentTrip.id,
                                                [locationIDKey]:
                                                    closestLocation.id,
                                            })
                                        } else {
                                            updateTripReport_LogBookEntrySectionLocationID(
                                                {
                                                    variables: {
                                                        input: {
                                                            id: currentTrip.id,
                                                            [locationIDKey]:
                                                                closestLocation.id,
                                                        },
                                                    },
                                                },
                                            )
                                        }
                                        setSelectedLocation(closestLocation)
                                        setOpenSetLocationDialog(false)
                                    }}
                                    className="mt-3 w-full"
                                    disabled={disabled}>
                                    Use {closestLocation.label}
                                </Button>
                            </div>
                            <Separator className="my-2" />
                            <P className="text-sm text-gray-600">
                                Or alternatively, create a new location:
                            </P>
                        </>
                    ) : (
                        <div className="bg-amber-50 p-4 rounded-lg border border-amber-200 mb-4">
                            <P className="text-sm text-amber-800">
                                Fetching current location is taking too long.
                                Create a new location instead?
                            </P>
                        </div>
                    )}
                    <div className="space-y-4 py-2">
                        <Label
                            htmlFor="new-location-title"
                            label="Location Name">
                            <Input
                                id="new-location-title"
                                type="text"
                                placeholder="Enter location name"
                                required
                                disabled={disabled}
                            />
                        </Label>

                        <Label
                            htmlFor="parent-location"
                            label="Parent Location (Optional)">
                            <Combobox
                                id="parent-location"
                                options={geoLocations ? allLocations() : []}
                                onChange={handleParentLocationChange}
                                placeholder="Select parent location"
                                isDisabled={disabled}
                            />
                        </Label>

                        <div className="grid grid-cols-2 gap-4">
                            <Label
                                htmlFor="new-location-latitude"
                                label="Latitude">
                                <Input
                                    id="new-location-latitude"
                                    type="text"
                                    defaultValue={location.latitude}
                                    placeholder="Enter latitude"
                                    required
                                    disabled={disabled}
                                />
                            </Label>

                            <Label
                                htmlFor="new-location-longitude"
                                label="Longitude">
                                <Input
                                    id="new-location-longitude"
                                    type="text"
                                    defaultValue={location.longitude}
                                    placeholder="Enter longitude"
                                    required
                                    disabled={disabled}
                                />
                            </Label>
                        </div>
                    </div>
                </AlertDialogNew>
            </div>
        </Label>
    )
}
