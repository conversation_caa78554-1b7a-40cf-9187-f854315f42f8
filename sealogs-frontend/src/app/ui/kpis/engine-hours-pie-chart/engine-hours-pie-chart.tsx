'use client'
import React, { useEffect, useState } from 'react'
import { isOverDueTask } from '@/app/lib/actions'
import { useLazyQuery } from '@apollo/client'
import { getPermissions, hasPermission } from '@/app/helpers/userHelper'
import { PieChartComponent } from '@/components/pie-chart'
import { ChartConfig } from '@/components/ui/chart'
import { CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { ReadComponentMaintenanceChecks } from './queries'

export default function EngineHoursPieChart() {
    const [maintenanceChecks, setMaintenanceChecks] = useState<any>()
    const [filter, setFilter] = useState({} as SearchFilter)
    const [isLoading, setIsLoading] = useState(true)
    const [keywordFilter, setKeywordFilter] = useState([] as any)
    const [permissions, setPermissions] = useState<any>(false)
    const [edit_task, setEdit_task] = useState<any>(false)
    const [overdueTasks, setOverdueTasks] = useState<any>()
    const [upcomingTasks, setUpcomingTasks] = useState<any>()
    const [extractedDays, setExtractedDays] = useState<any>()
    const [under30, setUnder30] = useState<any>()
    const [between30and100, setBetween30and100] = useState<any>()
    const [over100, setOver100] = useState<any>()

    const init_permissions = () => {
        if (permissions) {
            if (hasPermission('EDIT_TASK', permissions)) {
                setEdit_task(true)
            } else {
                setEdit_task(false)
            }
        }
    }

    useEffect(() => {
        setPermissions(getPermissions)
        init_permissions()
    }, [])

    useEffect(() => {
        init_permissions()
    }, [permissions])

    const [queryMaintenanceChecks] = useLazyQuery(
        ReadComponentMaintenanceChecks,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data = response.readComponentMaintenanceChecks.nodes
                if (data) {
                    handleSetMaintenanceChecks(data)
                }
            },
            onError: (error: any) => {
                console.error('queryMaintenanceChecks error', error)
            },
        },
    )
    useEffect(() => {
        if (isLoading) {
            loadMaintenanceChecks()
            setIsLoading(false)
        }
    }, [isLoading])
    const loadMaintenanceChecks = async (
        searchFilter: SearchFilter = { ...filter },
        searchkeywordFilter: any = keywordFilter,
    ) => {
        if (searchkeywordFilter.length > 0) {
            const promises = searchkeywordFilter.map(
                async (keywordFilter: any) => {
                    return await queryMaintenanceChecks({
                        variables: {
                            filter: { ...searchFilter, ...keywordFilter },
                        },
                    })
                },
            )
            let responses = await Promise.all(promises)
            // filter out empty results
            responses = responses.filter(
                (r: any) =>
                    r.data.readComponentMaintenanceChecks.nodes.length > 0,
            )
            // flatten results
            responses = responses.flatMap(
                (r: any) => r.data.readComponentMaintenanceChecks.nodes,
            )
            // filter out duplicates
            responses = responses.filter(
                (value: any, index: any, self: any) =>
                    self.findIndex((v: any) => v.id === value.id) === index,
            )
            handleSetMaintenanceChecks(responses)
        } else {
            await queryMaintenanceChecks({
                variables: {
                    filter: searchFilter,
                },
            })
        }
    }

    const handleSetMaintenanceChecks = (tasks: any) => {
        const activeTasks = tasks
            .filter((task: any) => task.archived === false)
            .map((task: any) => ({
                ...task,
                isOverDue: isOverDueTask(task),
            }))
        setMaintenanceChecks(activeTasks)
    }

    useEffect(() => {
        if (maintenanceChecks) {
            setOverdueTasks(
                maintenanceChecks.filter(
                    (task: any) =>
                        task.isOverDue.status === 'High' &&
                        task.isOverDue.days.includes('engine'),
                ).length,
            )
            setUpcomingTasks(
                maintenanceChecks.filter(
                    (task: any) => task.isOverDue.status === 'Upcoming',
                ),
            )
        }
    }, [maintenanceChecks])

    useEffect(() => {
        if (upcomingTasks) {
            setExtractedDays(
                upcomingTasks.filter((task: any) =>
                    task.isOverDue.days.includes('engine'),
                ),
            )
        }
    }, [upcomingTasks])

    useEffect(() => {
        if (extractedDays) {
            /*setUnder30(extractedDays.filter((task: any) =>
                ((task.isOverDue.days).match(/(\d+)/))[0] < 30
            ).length)

            setBetween30and100(extractedDays.filter((task: any) => {
                const days = ((task.isOverDue.days).match(/(\d+)/))[0]
                return days >= 30 && days <= 90;
            }).length)

            setOver100(extractedDays.filter((task: any) =>
                ((task.isOverDue.days).match(/(\d+)/))[0] > 90
            ).length)*/

            setUnder30(
                extractedDays.filter((task: any) => {
                    const match = (task.isOverDue.days || '').match(/(\d+)/)
                    const days = match ? parseInt(match[0], 10) : null
                    return days !== null && days < 30
                }).length,
            )

            setBetween30and100(
                extractedDays.filter((task: any) => {
                    const match = (task.isOverDue.days || '').match(/(\d+)/)
                    const days = match ? parseInt(match[0], 10) : null
                    return days !== null && days >= 30 && days <= 90
                }).length,
            )

            setOver100(
                extractedDays.filter((task: any) => {
                    const match = (task.isOverDue.days || '').match(/(\d+)/)
                    const days = match ? parseInt(match[0], 10) : null
                    return days !== null && days > 90
                }).length,
            )
        }
    }, [extractedDays])

    const chartData = [
        {
            title: 'Task overdue',
            amount: overdueTasks,
            fill: 'var(--color-overdue)',
            stroke: 'hsl(1, 97%, 60%)',
        },
        {
            title: 'Tasks due < 30',
            amount: under30,
            fill: 'var(--color-thirtyDays)',
            stroke: 'hsl(205, 78%, 48%)',
        },
        {
            title: 'Tasks due 30 - 100',
            amount: between30and100,
            fill: 'var(--color-thirtyToNinety)',
            stroke: 'hsl(205, 32%, 45%)',
        },
        {
            title: 'Tasks due > 100',
            amount: over100,
            fill: 'var(--color-ninetyPlus)',
            stroke: 'hsl(174, 100%, 40%)',
        },
        //{ title: "Completed", amount: 2, fill: "var(--color-completed)" },
    ]
    const chartConfig = {
        amount: {
            label: 'Amount',
        },
        overdue: {
            label: 'Task overdue based on engine hours',
            color: 'var(--chart-1)',
        },
        thirtyDays: {
            label: 'Tasks due within 30 hours',
            color: 'var(--chart-3)',
        },
        thirtyToNinety: {
            label: 'Tasks due 30 to 100 hours',
            color: 'var(--chart-4)',
        },
        ninetyPlus: {
            label: 'Tasks due in more than 100 hours',
            color: 'var(--chart-5)',
        },
        /*completed: {
            label: "Completed",
            color: "hsl(var(--chart-5))",
        },*/
    } satisfies ChartConfig


    return (
        <div>
            <PieChartComponent
                chartData={chartData}
                chartConfig={chartConfig}
            />
            <p>Tasks due - engine hours</p>
        </div>
    )
}
