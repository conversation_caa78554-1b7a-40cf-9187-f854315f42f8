'use client'
import React, { useState } from 'react'
import { useMutation } from '@apollo/client'
import { <PERSON>, Card<PERSON><PERSON>er, CardContent, CardFooter } from '@/components/ui/card'
import {
    UPDATE_SUPPLIER,
    CREATE_SEALOGS_FILE_LINKS,
} from '@/app/lib/graphQL/mutation'
import { useRouter } from 'next/navigation'
import { getSupplierByID } from '@/app/lib/actions'

import Link from 'next/link'
import SupplierContacts, { ISupplierContact } from './supplier-contacts'
import { DELETE_SUPPLIERS } from '@/app/lib/graphQL/mutation/DELETE_SUPPLIERS'
import { DELETE_SUPPLIER_CONTACTS } from '@/app/lib/graphQL/mutation/DELETE_SUPPLIER_CONTACTS'
import { CREATE_SUPPLIER_CONTACT } from '@/app/lib/graphQL/mutation/CREATE_SUPPLIER_CONTACT'
import { UPDATE_SUPPLIER_CONTACT } from '@/app/lib/graphQL/mutation/UPDATE_SUPPLIER_CONTACT'
import { Input } from '@/components/ui/input'
import {
    Building2,
    Globe,
    Phone,
    Mail,
    MapPin,
    StickyNote,
    Check,
    Trash2,
    ArrowLeft,
} from 'lucide-react'
import { Textarea } from '@/components/ui/textarea'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { FooterWrapper } from '@/components/footer-wrapper'
import SeaLogsButton from '@/components/ui/sea-logs-button'
import { toast } from 'sonner'

export default function Supplier({ supplierID }: { supplierID: number }) {
    const [supplier, setSupplier] = useState<any>()
    const router = useRouter()
    const [contactFields, setContactFields] = useState<ISupplierContact[]>([
        {
            name: '',
            email: '',
            phone: '',
        },
    ])
    const [fileLinks, setFileLinks] = useState<any>([])
    const [linkSelectedOption, setLinkSelectedOption] = useState<any>([])

    getSupplierByID(supplierID, (supplier: any) => {
        setSupplier(supplier)

        if (supplier.supplierContacts.nodes.length > 0) {
            const existingContactFields = supplier.supplierContacts.nodes.map(
                (item: any) => ({
                    id: item.id,
                    name: item.name ?? '',
                    phone: item.phone ?? '',
                    email: item.email ?? '',
                }),
            )

            setContactFields(existingContactFields)
        }
    })

    const handleSave = async (e: any) => {
        const name = (
            document.getElementById('supplier-name') as HTMLInputElement
        ).value
        const website = (
            document.getElementById('supplier-website') as HTMLInputElement
        ).value
        const phone = (
            document.getElementById('supplier-phone') as HTMLInputElement
        ).value
        const email = (
            document.getElementById('supplier-email') as HTMLInputElement
        ).value
        const address = (
            document.getElementById('supplier-address') as HTMLInputElement
        ).value
        // const contactPhone = (document.getElementById('supplier-contact_phone') as HTMLInputElement).value;
        // const contactEmail = (document.getElementById('supplier-contact_email') as HTMLInputElement).value;
        const notes = (
            document.getElementById('supplier-notes') as HTMLInputElement
        ).value

        if (name === '') {
            return toast.error("Please fill supplier's name!")
        }

        // if (contactFields.length > 0) {
        //     const anyContactFieldEmpty = contactFields.filter(
        //         (contactField) =>
        //             contactField.name == '' ||
        //             (contactField.phone == '' && contactField.email == ''),
        //     ).length

        //     if (anyContactFieldEmpty > 0) {
        //         return toast.error('Please complete contact data!')
        //     }
        // }

        const variables = {
            input: {
                id: parseInt(supplierID.toString()),
                name,
                address,
                website,
                email,
                phone,
                notes,
                // "contactPerson": supplier.ContactPerson,
            },
        }

        Promise.all([
            mutationUpdateSupplier({ variables }),
            handleUpdateOrCreateSupplierContacts(),
            handleDeleteUnusedContacts(),
        ])
            .then(() => router.back())
            .catch((error) => {
                toast.error('There was a problem updating supplier')

                console.error('Error updating supplier', error)
            })
    }

    const handleUpdateOrCreateSupplierContacts = async () => {
        contactFields.forEach(async (element) => {
            if (element.id === undefined) {
                await mutationCreateSupplierContact({
                    variables: {
                        input: { ...element, supplierID },
                    },
                })
            } else {
                await mutationUpdateSupplierContact({
                    variables: {
                        input: element,
                    },
                })
            }
        })
    }

    const handleDeleteUnusedContacts = async () => {
        const existingContactIDs = new Set(
            supplier.supplierContacts.nodes.map((item: any) => item.id),
        )
        const newContactIDs = new Set(
            contactFields
                .filter((item) => item.id !== undefined)
                .map((item) => item.id),
        )

        const shouldDeletedContactIDs =
            existingContactIDs.difference(newContactIDs)

        if (shouldDeletedContactIDs.size > 0) {
            await mutationDeleteSupplierContacts({
                variables: {
                    ids: Array.from(shouldDeletedContactIDs),
                },
            })
        }
    }

    const [mutationUpdateSupplier, { loading: mutationupdateSupplierLoading }] =
        useMutation(UPDATE_SUPPLIER, {
            onCompleted: (response: any) => {},
            onError: (error: any) => {
                console.error('mutationupdateSupplier error', error)
            },
        })

    const [
        mutationCreateSupplierContact,
        { loading: mutationCreateSupplierContactLoading },
    ] = useMutation(CREATE_SUPPLIER_CONTACT, {
        onCompleted: (response: any) => {},
        onError: (error: any) => {
            console.error('mutationCreateSupplierContact error', error)
        },
    })

    const [
        mutationUpdateSupplierContact,
        { loading: mutationUpdateSupplierContactLoading },
    ] = useMutation(UPDATE_SUPPLIER_CONTACT, {
        onCompleted: (response: any) => {},
        onError: (error: any) => {
            console.error('mutationUpdateSupplierContact error', error)
        },
    })

    const handleDeleteSupplier = async () => {
        Promise.all([
            mutationDeleteSuppliers({
                variables: {
                    ids: [supplierID],
                },
            }),
            mutationDeleteSupplierContacts({
                variables: {
                    ids: supplier.supplierContacts.nodes.map(
                        (item: any) => item.id,
                    ),
                },
            }),
        ])
            .then(() => {
                // toast.success('Delete supplier success!')

                router.back()
            })
            .catch((err) => {
                console.error(err)

                toast.error('There was a problem deleting supplier!')
            })
    }

    const [
        mutationDeleteSuppliers,
        { loading: mutationdeleteSupplierLoading },
    ] = useMutation(DELETE_SUPPLIERS, {
        onCompleted: (response: any) => {
            // const { isSuccess, data } = response.deleteSupplier
            // if (isSuccess) {
            //     router.back()
            // } else {
            //     console.error('mutationdeleteSupplier error', response)
            // }
        },
        onError: (error: any) => {
            console.error('mutationdeleteSupplier error', error)
        },
    })
    const [
        mutationDeleteSupplierContacts,
        { loading: mutationdeleteSupplierContactsLoading },
    ] = useMutation(DELETE_SUPPLIER_CONTACTS, {
        onCompleted: (response: any) => {
            // const { isSuccess, data } = response.deleteSupplier
            // if (isSuccess) {
            //     router.back()
            // } else {
            //     console.error('mutationdeleteSupplier error', response)
            // }
        },
        onError: (error: any) => {
            console.error('mutationdeleteSupplier error', error)
        },
    })

    const [createSeaLogsFileLinks] = useMutation(CREATE_SEALOGS_FILE_LINKS, {
        onCompleted: (response: any) => {
            const data = response.createSeaLogsFileLinks
            if (data.id > 0) {
                const newLinks = [...fileLinks, data]
                setFileLinks(newLinks)
                linkSelectedOption
                    ? setLinkSelectedOption([
                          ...linkSelectedOption,
                          { label: data.link, value: data.id },
                      ])
                    : setLinkSelectedOption([
                          { label: data.link, value: data.id },
                      ])
            }
        },
        onError: (error: any) => {
            console.error('createSeaLogsFileLinksEntry error', error)
        },
    })

    const handleDeleteLink = (link: any) => {
        setLinkSelectedOption(linkSelectedOption.filter((l: any) => l !== link))
    }

    const linkItem = (link: any) => {
        return (
            <div className="flex justify-between align-middle mr-2 w-fit">
                <Link href={link.label} target="_blank" className="ml-2 ">
                    {link.label}
                </Link>
                <div className="ml-2 ">
                    <SeaLogsButton
                        icon="cross_icon"
                        action={() => handleDeleteLink(link)}
                    />
                </div>
            </div>
        )
    }

    return (
        <Card>
            <CardHeader className="bg-muted/30 border-b">
                <div className="flex items-center gap-3">
                    <Building2 className="h-6 w-6 text-primary" />
                    <div>
                        <p className="text-sm text-muted-foreground">
                            Supplier
                        </p>
                        <h2 className="text-2xl font-medium">
                            {supplier?.name}
                        </h2>
                    </div>
                </div>
            </CardHeader>

            <CardContent className="p-6 space-y-8">
                {/* Supplier Details Section */}
                <div className="grid md:grid-cols-3 gap-8">
                    <div>
                        <h3 className="text-lg font-medium flex items-center gap-2 text-primary mb-2">
                            <Building2 className="h-5 w-5" />
                            Supplier Details
                        </h3>
                        <p className="text-sm text-muted-foreground">
                            Basic information about the supplier including
                            contact details and website.
                        </p>
                    </div>

                    <div className="md:col-span-2 space-y-6">
                        <div className="grid md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                                <label
                                    htmlFor="supplier-name"
                                    className="text-sm font-medium flex items-center gap-1">
                                    <Building2 className="h-4 w-4" />
                                    Company Name
                                </label>
                                <Input
                                    id="supplier-name"
                                    type="text"
                                    defaultValue={supplier?.name}
                                    placeholder="Supplier name"
                                    className="w-full"
                                />
                            </div>

                            <div className="space-y-2">
                                <label
                                    htmlFor="supplier-website"
                                    className="text-sm font-medium flex items-center gap-1">
                                    <Globe className="h-4 w-4" />
                                    Website
                                </label>
                                <Input
                                    id="supplier-website"
                                    type="text"
                                    defaultValue={supplier?.website}
                                    placeholder="Type the website and press Enter"
                                    className="w-full"
                                    onKeyDown={async (event) => {
                                        if (event.key === 'Enter') {
                                            const inputValue = (
                                                event.target as HTMLInputElement
                                            ).value
                                            await createSeaLogsFileLinks({
                                                variables: {
                                                    input: {
                                                        link: inputValue,
                                                    },
                                                },
                                            })
                                        }
                                    }}
                                />
                            </div>
                        </div>

                        {/* Links Section */}
                        {(linkSelectedOption?.length > 0 ||
                            fileLinks?.length > 0) && (
                            <div className="flex flex-wrap gap-2 p-3 bg-muted/20 rounded-md">
                                {linkSelectedOption
                                    ? linkSelectedOption.map(
                                          (link: {
                                              value:
                                                  | React.Key
                                                  | null
                                                  | undefined
                                          }) => (
                                              <div
                                                  key={link.value}
                                                  className="inline-block">
                                                  {linkItem(link)}
                                              </div>
                                          ),
                                      )
                                    : fileLinks.map(
                                          (link: {
                                              value:
                                                  | React.Key
                                                  | null
                                                  | undefined
                                          }) => (
                                              <div
                                                  key={link.value}
                                                  className="inline-block">
                                                  {linkItem(link)}
                                              </div>
                                          ),
                                      )}
                            </div>
                        )}

                        <div className="grid md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                                <label
                                    htmlFor="supplier-phone"
                                    className="text-sm font-medium flex items-center gap-1">
                                    <Phone className="h-4 w-4" />
                                    Phone
                                </label>
                                <Input
                                    id="supplier-phone"
                                    defaultValue={supplier?.phone}
                                    type="text"
                                    placeholder="Phone"
                                    className="w-full"
                                />
                            </div>

                            <div className="space-y-2">
                                <label
                                    htmlFor="supplier-email"
                                    className="text-sm font-medium flex items-center gap-1">
                                    <Mail className="h-4 w-4" />
                                    Email
                                </label>
                                <Input
                                    id="supplier-email"
                                    defaultValue={supplier?.email}
                                    type="email"
                                    placeholder="Email"
                                    className="w-full"
                                />
                            </div>
                        </div>

                        <div className="space-y-2">
                            <label
                                htmlFor="supplier-address"
                                className="text-sm font-medium flex items-center gap-1">
                                <MapPin className="h-4 w-4" />
                                Address
                            </label>
                            <Textarea
                                id="supplier-address"
                                defaultValue={supplier?.address}
                                placeholder="Supplier address"
                                className="w-full"
                            />
                        </div>
                    </div>
                </div>

                <Separator />

                {/* Contacts Section */}
                <Card>
                    <CardHeader className="p-4 bg-muted/20">
                        <h3 className="text-lg font-medium">Contact Persons</h3>
                    </CardHeader>
                    <CardContent className="p-4">
                        <SupplierContacts
                            data={contactFields}
                            setData={setContactFields}
                        />
                    </CardContent>
                </Card>

                <Separator />

                {/* Notes Section */}
                <div className="grid md:grid-cols-3 gap-8">
                    <div>
                        <h3 className="text-lg font-medium flex items-center gap-2 text-primary mb-2">
                            <StickyNote className="h-5 w-5" />
                            Notes
                        </h3>
                        <p className="text-sm text-muted-foreground">
                            Additional information about this supplier.
                        </p>
                    </div>

                    <div className="md:col-span-2">
                        <Textarea
                            id="supplier-notes"
                            rows={5}
                            defaultValue={supplier?.notes}
                            placeholder="Notes"
                            className="w-full"
                        />
                    </div>
                </div>
            </CardContent>

            <FooterWrapper className="flex justify-end gap-2.5">
                <Button size="sm" variant="back" onClick={() => router.back()}>
                    Back
                </Button>

                <Button
                    size="sm"
                    variant="destructive"
                    onClick={handleDeleteSupplier}>
                    <span className="hidden sm:inline">Delete Supplier</span>
                    <span className="sm:hidden">Delete</span>
                </Button>

                <Button size="sm" onClick={handleSave}>
                    <span className="hidden sm:inline">Update Supplier</span>
                    <span className="sm:hidden">Update</span>
                </Button>
            </FooterWrapper>
        </Card>
    )
}
