'use client'
import React, { useState } from 'react'

import { useMutation } from '@apollo/client'
import Link from 'next/link'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'

import { toast } from 'sonner'

import { X } from 'lucide-react'
import {
    CREATE_SUPPLIER,
    CREATE_SEALOGS_FILE_LINKS,
} from '@/app/lib/graphQL/mutation'
import { useRouter } from 'next/navigation'

import { CREATE_SUPPLIER_CONTACT } from '@/app/lib/graphQL/mutation/CREATE_SUPPLIER_CONTACT'
import { FooterWrapper } from '@/components/footer-wrapper'
import {
    Card,
    CardHeader,
    CardContent,
    H4,
    P,
    ListHeader,
} from '@/components/ui'
import SupplierContacts, { ISupplierContact } from './supplier-contacts'

export default function NewSupplier({ supplierId }: { supplierId: number }) {
    const router = useRouter()

    const [fileLinks, setFileLinks] = useState<any>([])
    const [linkSelectedOption, setLinkSelectedOption] = useState<any>([])
    const [contactFields, setContactFields] = useState<ISupplierContact[]>([
        {
            name: '',
            email: '',
            phone: '',
        },
    ])

    const handleCreate = async () => {
        const name = (
            document.getElementById('supplier-name') as HTMLInputElement
        ).value
        const website = (
            document.getElementById('supplier-website') as HTMLInputElement
        ).value
        const phone = (
            document.getElementById('supplier-phone') as HTMLInputElement
        ).value
        const email = (
            document.getElementById('supplier-email') as HTMLInputElement
        ).value
        const address = (
            document.getElementById('supplier-address') as HTMLInputElement
        ).value
        const notes = (
            document.getElementById('supplier-notes') as HTMLInputElement
        ).value

        const variables = {
            input: {
                name,
                address,
                website,
                email,
                phone,
                notes,
            },
        }

        if (name === '') {
            return toast.error("Please fill supplier's name!")
        }

        // if (contactFields.length > 0) {
        //     const anyContactFieldEmpty = contactFields.filter(
        //         (contactField) =>
        //             contactField.name == '' ||
        //             (contactField.phone == '' && contactField.email == ''),
        //     ).length

        //     if (anyContactFieldEmpty > 0) {
        //         return toast.error('Please complete contact data!')
        //     }
        // }

        const response = await mutationCreateSupplier({
            variables,
        })

        const supplierID = response.data?.createSupplier.id ?? 0
        if (supplierID == 0) {
            return toast.error('Error creating new supplier')
        }

        contactFields.forEach(async (element) => {
            const variableContact = { ...element, supplierID }
            delete variableContact.id

            await mutationCreateSupplierContact({
                variables: {
                    input: variableContact,
                },
            })
        })

        router.back()
    }

    const [mutationCreateSupplier, { loading: mutationcreateSupplierLoading }] =
        useMutation(CREATE_SUPPLIER, {
            onCompleted: (response: any) => {},
            onError: (error: any) => {
                console.error('mutationcreateSupplier error', error)
            },
        })

    const [
        mutationCreateSupplierContact,
        { loading: mutationcreateSupplierContactLoading },
    ] = useMutation(CREATE_SUPPLIER_CONTACT, {
        onCompleted: (response: any) => {},
        onError: (error: any) => {
            console.error('mutationcreateSupplierContact error', error)
        },
    })

    const [createSeaLogsFileLinks] = useMutation(CREATE_SEALOGS_FILE_LINKS, {
        onCompleted: (response: any) => {
            const data = response.createSeaLogsFileLinks
            if (data.id > 0) {
                const newLinks = [...fileLinks, data]
                setFileLinks(newLinks)
                linkSelectedOption
                    ? setLinkSelectedOption([
                          ...linkSelectedOption,
                          { label: data.link, value: data.id },
                      ])
                    : setLinkSelectedOption([
                          { label: data.link, value: data.id },
                      ])
            }
        },
        onError: (error: any) => {
            console.error('createSeaLogsFileLinksEntry error', error)
        },
    })

    const handleDeleteLink = (link: any) => {
        setLinkSelectedOption(linkSelectedOption.filter((l: any) => l !== link))
    }

    const linkItem = (link: any) => {
        return (
            <div className="flex justify-between align-middle mr-2 w-fit">
                <Link href={link.label} target="_blank" className="ml-2 ">
                    {link.label}
                </Link>
                <div className="ml-2 ">
                    <Button
                        variant="ghost"
                        size="icon"
                        iconLeft={X}
                        iconOnly
                        onClick={() => handleDeleteLink(link)}
                    />
                </div>
            </div>
        )
    }

    return (
        <div className="space-y-6">
            <ListHeader title="New Supplier" />

            <Card className="mx-2.5">
                <CardHeader>
                    <H4>Company Information</H4>
                    <P>
                        Enter the basic information about the supplier company
                        including contact details and address.
                    </P>
                </CardHeader>
                <CardContent className="space-y-8">
                    <div className="space-y-5">
                        <Label label="Company Name" htmlFor="supplier-name">
                            <Input
                                id="supplier-name"
                                type="text"
                                placeholder="Supplier name"
                                className="w-full"
                            />
                        </Label>

                        <Label label="Website" htmlFor="supplier-website">
                            <Input
                                id="supplier-website"
                                type="text"
                                placeholder="Company website"
                                className="w-full"
                                onKeyDown={async (event) => {
                                    if (event.key === 'Enter') {
                                        const inputValue = (
                                            event.target as HTMLInputElement
                                        ).value
                                        await createSeaLogsFileLinks({
                                            variables: {
                                                input: {
                                                    link: inputValue,
                                                },
                                            },
                                        })
                                        // toast.success(
                                        //     `Added ${inputValue} to supplier links`,
                                        // )
                                    }
                                }}
                            />
                        </Label>

                        {/* Website Links */}
                        {(linkSelectedOption || fileLinks).length > 0 && (
                            <div className="w-full">
                                <Label label="Linked Websites">
                                    <div className="flex flex-wrap gap-2">
                                        {linkSelectedOption
                                            ? linkSelectedOption.map(
                                                  (link: {
                                                      value: string | number
                                                      label: string
                                                  }) => (
                                                      <div
                                                          key={link.value}
                                                          className="inline-block">
                                                          {linkItem(link)}
                                                      </div>
                                                  ),
                                              )
                                            : fileLinks.map(
                                                  (link: {
                                                      value:
                                                          | React.Key
                                                          | null
                                                          | undefined
                                                  }) => (
                                                      <div
                                                          key={link.value}
                                                          className="inline-block">
                                                          {linkItem(link)}
                                                      </div>
                                                  ),
                                              )}
                                    </div>
                                </Label>
                            </div>
                        )}

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                            <Label
                                label="Phone Number"
                                htmlFor="supplier-phone">
                                <Input
                                    id="supplier-phone"
                                    type="text"
                                    placeholder="Phone number"
                                />
                            </Label>

                            <Label
                                label="Email Address"
                                htmlFor="supplier-email">
                                <Input
                                    id="supplier-email"
                                    type="email"
                                    placeholder="Email address"
                                />
                            </Label>
                        </div>

                        <Label label="Address" htmlFor="supplier-address">
                            <Textarea
                                id="supplier-address"
                                rows={3}
                                placeholder="Supplier address"
                                className="resize-none"
                            />
                        </Label>
                    </div>
                </CardContent>
            </Card>

            <Card className="mx-2.5">
                <CardHeader>
                    <H4>Contact Persons</H4>
                    <P>
                        Enter the contact details (name, phone, and email) of
                        the supplier's representative.
                    </P>
                </CardHeader>
                <CardContent>
                    <SupplierContacts
                        data={contactFields}
                        setData={setContactFields}
                    />
                </CardContent>
            </Card>

            <Card className="mx-2.5">
                <CardHeader>
                    <H4>Additional Notes</H4>
                    <P>
                        Add any additional information about this supplier that
                        might be useful.
                    </P>
                </CardHeader>
                <CardContent>
                    <Label label="Notes" htmlFor="supplier-notes">
                        <Textarea
                            id="supplier-notes"
                            rows={5}
                            placeholder="Enter any additional notes about this supplier..."
                            className="resize-none"
                        />
                    </Label>
                </CardContent>
            </Card>

            <FooterWrapper>
                <Button size="sm" variant="back" onClick={() => router.back()}>
                    Cancel
                </Button>
                <Button size="sm" onClick={handleCreate}>
                    Create supplier
                </Button>
            </FooterWrapper>
        </div>
    )
}
