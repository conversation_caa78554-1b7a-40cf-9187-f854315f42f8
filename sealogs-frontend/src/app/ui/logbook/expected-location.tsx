'use client'

import React, { useState, useEffect, useRef } from 'react'
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from '@/components/ui/popover'
import {
    CREATE_GEO_LOCATION,
    CreateFavoriteLocation,
} from '@/app/lib/graphQL/mutation'
import { useLazyQuery, useMutation } from '@apollo/client'
import { UpdateTripReport_LogBookEntrySection } from '@/app/lib/graphQL/mutation'
import { GetFavoriteLocations } from '@/app/lib/graphQL/query'
import FavoriteLocationModel from '@/app/offline/models/favoriteLocation'
import TripReport_LogBookEntrySectionModel from '@/app/offline/models/tripReport_LogBookEntrySection'
import { generateUniqueId } from '@/app/offline/helpers/functions'
import GeoLocationModel from '@/app/offline/models/geoLocation'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Combobox } from '@/components/ui/comboBox'
import { Button } from '@/components/ui/button'
import { toast } from 'sonner'
import { AlertDialogNew } from '@/components/ui/alert-dialog-new'
import { AlertCircleIcon } from 'lucide-react'

export default function ExpectedDestination({
    geoLocations = false,
    currentTrip,
    tripReport,
    templateStyle = false,
    offline = false,
    updateTripReport,
}: {
    geoLocations: any
    currentTrip: any
    tripReport: any
    templateStyle: boolean | string
    offline?: boolean
    updateTripReport?: any
}) {
    const latInputRef = useRef<HTMLInputElement | null>(null)
    const longInputRef = useRef<HTMLInputElement | null>(null)

    const favoriteLocationModel = new FavoriteLocationModel()
    const tripReportModel = new TripReport_LogBookEntrySectionModel()
    const geoLocationsModel = new GeoLocationModel()
    const [showLocation, setShowLocation] = useState(false)
    const [newLocation, setNewLocation] = useState(false)
    const [closestLocation, setClosestLocation] = useState<any>(false)
    const [selectedLocation, setSelectedLocation] = useState<any>()
    const [favoriteLocations, setFavoriteLocations] = useState<any>([])
    const [selectedParentLocation, setSelectedParentLocation] =
        useState<any>(false)
    const [locations, setLocations] = useState<any>()
    const [openNewLocationDialog, setOpenNewLocationDialog] =
        useState<boolean>(false)
    const [openSetLocationDialog, setOpenSetLocationDialog] =
        useState<boolean>(false)
    const [location, setLocation] = useState<{
        latitude: string
        longitude: string
    }>({ latitude: '', longitude: '' })

    const hideNewLocation = () => {
        setShowLocation(false)
        setNewLocation(false)
    }

    useEffect(() => {
        if (tripReport) {
            setSelectedLocation(false)
            var trip = tripReport.find(
                (trip: any) => trip.id === currentTrip.id,
            )
            if (trip?.toLocation?.id > 0) {
                setSelectedLocation({
                    label: trip.toLocation.title,
                    value: trip.toLocation.id,
                    latitude: trip.toLocation.lat,
                    longitude: trip.toLocation.long,
                })
                setLocation({
                    latitude: trip.toLocation.lat,
                    longitude: trip.toLocation.long,
                })
                setShowLocation(false)
            } else {
                if (trip?.toLat != 0 && trip?.toLong != 0) {
                    setLocation({
                        latitude: trip?.toLat,
                        longitude: trip?.toLong,
                    })
                    setShowLocation(true)
                }
            }
        }
    }, [tripReport])

    const handleLocationChange = async (selectedLocation: any) => {
        if (selectedLocation.value === 'newLocation') {
            setSelectedParentLocation(false)
            setOpenNewLocationDialog(true)
        } else {
            setSelectedLocation(selectedLocation)
            if (offline) {
                const data = await tripReportModel.save({
                    id: currentTrip.id,
                    toLocationID: selectedLocation.value,
                })
                updateTripReport({
                    id: [...tripReport.map((trip: any) => trip.id), data.id],
                    currentTripID: currentTrip.id,
                    key: 'toLocationID',
                    value: selectedLocation.value,
                })
            } else {
                updateTripReport_LogBookEntrySectionLocationID({
                    variables: {
                        input: {
                            id: currentTrip.id,
                            toLocationID: selectedLocation.value,
                        },
                    },
                })
            }
            const userId = localStorage.getItem('userId')
            if (userId !== null && +userId > 0) {
                // if (+localStorage.getItem('userId')> 0) {
                if (offline) {
                    await favoriteLocationModel.save({
                        id: generateUniqueId(),
                        memberID: +localStorage.getItem('userId')!,
                        geoLocationID: +selectedLocation.value,
                        usage: 1,
                    })
                    const locations = await favoriteLocationModel.getByMemberID(
                        {
                            memberID: +localStorage.getItem('userId')!,
                        },
                    )

                    setFavoriteLocations(locations)
                } else {
                    createFavoriteLocation({
                        variables: {
                            input: {
                                memberID: +localStorage.getItem('userId')!,
                                geoLocationID: +selectedLocation.value,
                            },
                        },
                    })
                }
            }
        }
    }
    const offlineGetFavoriteLocations = async () => {
        const locations = await favoriteLocationModel.getByMemberID(
            +localStorage.getItem('userId')!,
        )
        setFavoriteLocations(locations)
    }
    useEffect(() => {
        if (!offline) {
            getFavoriteLocations({
                variables: {
                    userID: +localStorage.getItem('userId')!,
                },
            })
        } else {
            offlineGetFavoriteLocations()
        }
    }, [])

    const [getFavoriteLocations] = useLazyQuery(GetFavoriteLocations, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (data) => {
            const locations = data.readFavoriteLocations.nodes
            setFavoriteLocations(locations)
        },
        onError: (error) => {
            console.error('onError', error)
        },
    })

    const [createFavoriteLocation] = useMutation(CreateFavoriteLocation, {
        onCompleted: (data) => {},
        onError: (error) => {
            console.error('onError', error)
        },
    })

    const handleParentLocationChange = (selectedLocation: any) => {
        setSelectedParentLocation(selectedLocation)
    }

    const handleCreateNewLocation = async () => {
        const title = (
            document.getElementById('new-location-title') as HTMLInputElement
        )?.value
        const parentLocation = selectedParentLocation
            ? selectedParentLocation.value
            : null
        const latitude = (
            document.getElementById('new-location-latitude') as HTMLInputElement
        )?.value
        const longitude = (
            document.getElementById(
                'new-location-longitude',
            ) as HTMLInputElement
        )?.value

        const variables = {
            input: {
                title: title,
                lat: +latitude,
                long: +longitude,
                parentLocationID: parentLocation,
            },
        }
        if (offline) {
            const data = await geoLocationsModel.save({
                id: generateUniqueId(),
                ...variables.input,
            })
            if (locations?.length > 0) {
                setLocations([...locations, data])
            } else {
                setLocations([data])
            }
            setSelectedLocation({
                label: data.title,
                value: data.id,
                latitude: data.lat,
                longitude: data.long,
            })
            setOpenNewLocationDialog(false)
            setOpenSetLocationDialog(false)
        } else {
            createGeoLocation({
                variables,
            })
        }
    }

    const [createGeoLocation] = useMutation(CREATE_GEO_LOCATION, {
        onCompleted: (response) => {
            const data = response.createGeoLocation
            if (locations?.length > 0) {
                setLocations([...locations, data])
            } else {
                setLocations([data])
            }
            setSelectedLocation({
                label: data.title,
                value: data.id,
                latitude: data.lat,
                longitude: data.long,
            })
            setOpenNewLocationDialog(false)
            setOpenSetLocationDialog(false)
        },
        onError: (error) => {
            toast.error('Error creating GeoLocation')
            console.error('Error creating GeoLocation: ' + error.message)
            setOpenNewLocationDialog(false)
            setOpenSetLocationDialog(false)
            console.error('onError', error)
        },
    })

    const allLocations = () => {
        if (geoLocations && locations?.length > 0) {
            return sortLocations([
                ...locations,
                ...geoLocations?.map((location: any) => ({
                    label: location.title,
                    value: location.id,
                    latitude: location.lat,
                    longitude: location.long,
                })),
            ])
        } else {
            return sortLocations([
                ...geoLocations?.map((location: any) => ({
                    label: location.title,
                    value: location.id,
                    latitude: location.lat,
                    longitude: location.long,
                })),
            ])
        }
    }

    const sortLocations = (locations: any) => {
        favoriteLocations.length > 0
            ? locations.sort((a: any, b: any) => {
                  const aFav = favoriteLocations.find(
                      (fav: any) => fav.geoLocationID === a.value,
                  )
                  const bFav = favoriteLocations.find(
                      (fav: any) => fav.geoLocationID === b.value,
                  )
                  if (aFav && bFav) {
                      return bFav.usage - aFav.usage
                  } else if (aFav) {
                      return -1
                  } else if (bFav) {
                      return 1
                  }
                  return 0
              })
            : locations
        return locations
    }

    const getProximity = (location: any, latitude: any, longitude: any) => {
        const distance = Math.sqrt(
            Math.pow(location.lat - latitude, 2) +
                Math.pow(location.long - longitude, 2),
        )
        return distance
    }

    const findClosestLocation = async (latitude: any, longitude: any) => {
        const closestLocation = geoLocations.reduce((prev: any, curr: any) => {
            const prevDistance = Math.sqrt(
                Math.pow(prev.lat - latitude, 2) +
                    Math.pow(prev.long - longitude, 2),
            )
            const currDistance = Math.sqrt(
                Math.pow(curr.lat - latitude, 2) +
                    Math.pow(curr.long - longitude, 2),
            )
            return prevDistance < currDistance ? prev : curr
        })
        const proximity = getProximity(closestLocation, latitude, longitude)
        setClosestLocation({
            label: closestLocation.title,
            value: closestLocation.id,
            latitude: closestLocation.lat,
            longitude: closestLocation.long,
        })
        if (
            proximity > 0.15 ||
            (closestLocation.lat === 0 && closestLocation.long === 0)
        ) {
            toast.error('No location found within 10 KM radius!')
            return
        }
        if (offline) {
            await tripReportModel.save({
                id: currentTrip.id,
                toLocationID: closestLocation.id,
            })
        } else {
            updateTripReport_LogBookEntrySectionLocationID({
                variables: {
                    input: {
                        id: currentTrip.id,
                        toLocationID: closestLocation.id,
                    },
                },
            })
        }
    }

    const getSelectedLocation = () => {
        if (selectedLocation) return selectedLocation
        if (tripReport) {
            const trip = tripReport?.find(
                (trip: any) => trip.id === currentTrip.id,
            )
            if (trip?.toLocation?.id > 0) {
                return {
                    label: trip.toLocation.title,
                    value: trip.toLocation.id,
                    latitude: trip.toLocation.lat,
                    longitude: trip.toLocation.long,
                }
            }
        }
        return selectedLocation
    }

    const handleSetCurrentLocation = () => {
        toast.dismiss()
        toast.loading('Getting your current location...', {
            description: 'Please wait while we fetch your location.',
        })
        setOpenSetLocationDialog(true)
        if ('geolocation' in navigator) {
            const options = {
                timeout: 30000, // 30 seconds
            }
            navigator.geolocation.getCurrentPosition(
                ({ coords }) => {
                    const { latitude, longitude } = coords
                    setLocation({
                        latitude: latitude + '',
                        longitude: longitude + '',
                    })
                    findClosestLocation(latitude, longitude)
                },
                (error) => {
                    toast.error('Failed to get current location')
                },
                options,
            )
        } else {
            toast.error('Geolocation is not supported by your browser')
        }
    }

    const [updateTripReport_LogBookEntrySectionLocationID] = useMutation(
        UpdateTripReport_LogBookEntrySection,
        {
            onCompleted: (data) => {
                // toast.success('Location updated successfully')
            },
            onError: (error) => {
                console.error('onError', error)
            },
        },
    )

    const [updateTripReport_LogBookEntrySectionLocation] = useMutation(
        UpdateTripReport_LogBookEntrySection,
        {
            onCompleted: (data) => {
                // toast.success('Location updated successfully')
            },
            onError: (error) => {
                console.error('onError', error)
            },
        },
    )

    const handleSetLocation = async () => {
        if (offline) {
            await tripReportModel.save({
                id: currentTrip.id,
                toLocationID: closestLocation.id,
            })
        } else {
            updateTripReport_LogBookEntrySectionLocationID({
                variables: {
                    input: {
                        id: currentTrip.id,
                        toLocationID: closestLocation.id,
                    },
                },
            })
        }

        setSelectedLocation(closestLocation)
        setOpenSetLocationDialog(false)
    }

    const updateLocationCoordinates = async () => {
        const latitude = latInputRef.current?.value ?? ''
        const longitude = longInputRef.current?.value ?? ''

        setLocation({ latitude: latitude + '', longitude: longitude + '' })
        if (offline) {
            await tripReportModel.save({
                id: currentTrip.id,
                toLat: +latitude,
                toLong: +longitude,
                toLocationID: 0,
            })
        } else {
            updateTripReport_LogBookEntrySectionLocation({
                variables: {
                    input: {
                        id: currentTrip.id,
                        toLat: +latitude,
                        toLong: +longitude,
                        toLocationID: 0,
                    },
                },
            })
        }
    }

    return (
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 items-center  ">
            {templateStyle === false && (
                <Label className="md:block pb-1 md:pb-0">
                    Expected Location
                </Label>
            )}
            <div
                className={`${templateStyle === false ? 'grid-cols-1 md:col-span-2 lg:col-span-3' : 'grid-cols-1 md:col-span-3 lg:col-span-4'} flex flex-col`}>
                <div className="flex md:items-center flex-col md:flex-row gap-4">
                    {geoLocations && !showLocation && (
                        <Combobox
                            id="depart-location"
                            options={
                                geoLocations
                                    ? [
                                          {
                                              label: ' --- Add New Location --- ',
                                              value: 'newLocation',
                                          },
                                          ...allLocations(),
                                      ]
                                    : [
                                          {
                                              label: ' --- Add New Location --- ',
                                              value: 'newLocation',
                                          },
                                      ]
                            }
                            value={getSelectedLocation()}
                            onChange={handleLocationChange}
                            placeholder="Select location"
                        />
                    )}
                    {showLocation && (
                        <div className="flex flex-col items-start md:flex-row md:items-start gap-4">
                            <div className="flex flex-col">
                                <div className="grid md:flex grid-cols-2 gap-4">
                                    <div>
                                        <Input
                                            name="latitude"
                                            type="text"
                                            ref={latInputRef}
                                            value={location.latitude}
                                            onBlur={updateLocationCoordinates}
                                            onChange={(e) =>
                                                setLocation({
                                                    ...location,
                                                    latitude: e.target.value,
                                                })
                                            }
                                            className={` md:min-w-64`}
                                            aria-describedby="latitude-error"
                                            placeholder="Latitude"
                                            required
                                        />
                                    </div>
                                    <div>
                                        <Input
                                            name="longitude"
                                            type="text"
                                            ref={longInputRef}
                                            value={location.longitude}
                                            onBlur={updateLocationCoordinates}
                                            onChange={(e) =>
                                                setLocation({
                                                    ...location,
                                                    longitude: e.target.value,
                                                })
                                            }
                                            className={` md:min-w-64`}
                                            aria-describedby="longitude-error"
                                            required
                                            placeholder="Longitude"
                                        />
                                    </div>
                                </div>
                                <p>
                                    Enter the location lat-long using decimal
                                    degrees (eg: 37.80255 -122.41463)
                                </p>
                            </div>
                            <Button onClick={hideNewLocation}>
                                Use Location
                            </Button>
                        </div>
                    )}
                    <div className="flex flex-row gap-3 w-full md:w-auto">
                        {!showLocation && (
                            <>
                                <Button
                                    variant="secondary"
                                    onClick={() => setShowLocation(true)}>
                                    Use Coordinates
                                </Button>
                                <Button
                                    variant="secondary"
                                    onClick={handleSetCurrentLocation}>
                                    Current Location
                                </Button>
                                <Popover>
                                    <PopoverTrigger asChild>
                                        <Button size="icon">
                                            <AlertCircleIcon className="h-4 w-4" />
                                        </Button>
                                    </PopoverTrigger>
                                    <PopoverContent className="w-72">
                                        This automatically sets the nearest
                                        location available in locations list
                                        using your current GPS location.
                                    </PopoverContent>
                                </Popover>
                            </>
                        )}
                    </div>
                </div>
                {newLocation && (
                    <div className="flex flex-col gap-3 mt-5 flex-wrap">
                        <Label>Add New Location</Label>
                        <div className="flex flex-row gap-3 flex-wrap">
                            <div>
                                <Input
                                    id="title"
                                    name="title"
                                    type="text"
                                    aria-describedby="title-error"
                                    required
                                    placeholder="Title"
                                />
                            </div>
                            <div>
                                <Input
                                    id="shortcode"
                                    name="shortcode"
                                    type="text"
                                    aria-describedby="shortcode-error"
                                    required
                                    placeholder="Shortcode"
                                />
                            </div>
                            <div>
                                <Input
                                    id="parent-location"
                                    name="parent-location"
                                    type="text"
                                    aria-describedby="parent-location-error"
                                    required
                                    placeholder="Parent Location"
                                />
                            </div>
                            <div>
                                <Input
                                    id="latitude"
                                    name="latitude"
                                    type="text"
                                    defaultValue={location.latitude}
                                    aria-describedby="latitude-error"
                                    required
                                    placeholder="Latitude"
                                />
                            </div>
                            <div>
                                <Input
                                    id="longitude"
                                    name="longitude"
                                    type="text"
                                    defaultValue={location.longitude}
                                    aria-describedby="longitude-error"
                                    required
                                    placeholder="Longitude"
                                />
                            </div>
                            <div>
                                <Input
                                    id="sort-order"
                                    name="sort-order"
                                    type="text"
                                    aria-describedby="sort-order-error"
                                    required
                                    placeholder="Sort Order"
                                />
                            </div>
                        </div>
                        <div className="flex flex-row gap-3 flex-wrap">
                            <Button onClick={() => {}}>Save Location</Button>
                            <Button onClick={hideNewLocation}>Cancel</Button>
                        </div>
                    </div>
                )}
            </div>
            <AlertDialogNew
                openDialog={openNewLocationDialog}
                setOpenDialog={setOpenNewLocationDialog}
                actionText="Add New Location"
                handleCreate={handleCreateNewLocation}
                title="Add New Location">
                <div className="my-4 flex items-center">
                    <Input
                        id="new-location-title"
                        type="text"
                        aria-describedby="title-error"
                        required
                        placeholder="Location Title"
                    />
                </div>
                <div className="mb-4 flex items-center">
                    <Combobox
                        id="parent-location"
                        options={geoLocations ? allLocations() : []}
                        onChange={handleParentLocationChange}
                        placeholder="Parent Location (Optional)"
                    />
                </div>
                <div className="mb-4 flex items-center">
                    <Input
                        id="new-location-latitude"
                        type="text"
                        defaultValue={location.latitude}
                        aria-describedby="latitude-error"
                        required
                        placeholder="Latitude"
                    />
                </div>
                <div className="flex items-center">
                    <Input
                        id="new-location-longitude"
                        type="text"
                        defaultValue={location.longitude}
                        aria-describedby="longitude-error"
                        required
                        placeholder="Longitude"
                    />
                </div>
            </AlertDialogNew>
            <AlertDialogNew
                openDialog={openSetLocationDialog}
                setOpenDialog={setOpenSetLocationDialog}
                actionText="Create location"
                handleCreate={handleCreateNewLocation}>
                {closestLocation?.label != undefined ? (
                    <>
                        <div className="mb-2">
                            Are you in {closestLocation?.label}?
                        </div>
                        <Button variant="secondary" onClick={handleSetLocation}>
                            Use {closestLocation?.label}
                        </Button>
                        <hr className="my-4" />
                        <div>
                            or alternatively do you want to save location?2
                        </div>
                    </>
                ) : (
                    <div>
                        Fetching current location took long, do you want to
                        create a new location instead?
                    </div>
                )}
                <div className="my-4 flex items-center">
                    <Input
                        id="new-location-title"
                        type="text"
                        aria-describedby="title-error"
                        required
                        placeholder="Location Title"
                    />
                </div>
                <div className="mb-4 flex items-center">
                    <Combobox
                        id="parent-location"
                        options={geoLocations ? allLocations() : []}
                        onChange={handleParentLocationChange}
                        placeholder="Parent Location (Optional)"
                    />
                </div>
                <div className="mb-4 flex items-center">
                    <Input
                        id="new-location-latitude"
                        type="text"
                        defaultValue={location.latitude}
                        aria-describedby="latitude-error"
                        required
                        placeholder="Latitude"
                    />
                </div>
                <div className="flex items-center">
                    <Input
                        id="new-location-longitude"
                        type="text"
                        defaultValue={location.longitude}
                        aria-describedby="longitude-error"
                        required
                        placeholder="Longitude"
                    />
                </div>
            </AlertDialogNew>
        </div>
    )
}
