'use client'
import { getV<PERSON>elList } from '@/app/lib/actions'
import {
    DeleteTripReportSchedules,
    UpdateTripReportSchedule,
} from '@/app/lib/graphQL/mutation'
import {
    GET_GEO_LOCATIONS,
    ReadOneTripReportSchedule,
    ReadTripScheduleServices,
} from '@/app/lib/graphQL/query'
import { useLazyQuery, useMutation } from '@apollo/client'
import { isEmpty, trim } from 'lodash'
import { useRouter, useSearchParams } from 'next/navigation'
import { useEffect, useState } from 'react'
import dayjs from 'dayjs'
import DateField from '@/components/ui/date-field'
import TripReportScheduleStopList from './trip-report-schedule-stop-list'

// Shadcn UI components
import { Input } from '@/components/ui/input'

import { Combobox } from '@/components/ui/comboBox'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import { H2, H4 } from '@/components/ui/typography'
import { toast } from 'sonner'

// Lucide icons
import { Save, Trash2, X } from 'lucide-react'
import TimeField from '../logbook/components/time'
import { Button } from '@/components/ui/button'
import { AlertDialogNew } from '@/components/ui'
const TripReportScheduleForm = () => {
    const router = useRouter()
    const searchParams = useSearchParams()
    const id = searchParams.get('id') ?? 0
    const [trs, setTrs] = useState({} as any)
    const [vessels, setVessels] = useState<any>([])
    const [selectedVessels, setSelectedVessels] = useState<any>([])
    const [tripScheduleServices, setTripScheduleServices] = useState<any>([])
    const [selectedTripScheduleService, setSelectedTripScheduleService] =
        useState<any>(null)
    const [geoLocations, setGeoLocations] = useState<any>([])
    const [selectedFromLocation, setSelectedFromLocation] = useState<any>(null)
    const [selectedToLocation, setSelectedToLocation] = useState<any>(null)
    const scheduleTypes = [
        { value: 'EveryDay', label: 'Every Day' },
        { value: 'WeekDays', label: 'Days of the Week' },
    ]
    const [selectedScheduleType, setSelectedScheduleType] = useState<any>(
        scheduleTypes[0],
    )
    const daysOfWeek = [
        { value: 'Monday', label: 'Monday' },
        { value: 'Tuesday', label: 'Tuesday' },
        { value: 'Wednesday', label: 'Wednesday' },
        { value: 'Thursday', label: 'Thursday' },
        { value: 'Friday', label: 'Friday' },
        { value: 'Saturday', label: 'Saturday' },
        { value: 'Sunday', label: 'Sunday' },
        { value: 'Public Holidays', label: 'Public Holidays' },
    ]
    const [selectedDaysOfWeek, setSelectedDaysOfWeek] = useState<any>([])
    const [openConfirmDeleteDialog, setOpenConfirmDeleteDialog] =
        useState(false)
    const handleOnChangeTitle = (e: any) => {
        setTrs({ ...trs, title: e.target.value })
    }
    const handleTripScheduleServiceChange = (e: any) => {
        setSelectedTripScheduleService(e)
        setTrs({
            ...trs,
            tripScheduleServiceID: e.value,
        })
    }
    const handleOnChangeTransitRouteID = (e: any) => {
        setTrs({ ...trs, transitRouteID: e.target.value })
    }
    const handleOnChangeTransitTripID = (e: any) => {
        setTrs({ ...trs, transitTripID: e.target.value })
    }
    const handleVesselChange = (e: any) => {
        setSelectedVessels(e)
        setTrs({
            ...trs,
            vehicles: e.map((v: any) => v.value).join(','),
        })
    }
    const handleOnChangeDepartTime = (e: any) => {
        setTrs({
            ...trs,
            departTime: dayjs(e).format('HH:mm:ss'),
        })
    }
    const handleOnChangeArriveTime = (e: any) => {
        setTrs({
            ...trs,
            arriveTime: dayjs(e).format('HH:mm:ss'),
        })
    }
    const handleOnChangeExpectedContactTime = (e: any) => {
        setTrs({
            ...trs,
            expectedContactTime: dayjs(e).format('HH:mm:ss'),
        })
    }
    const handleOnChangeFromLocation = (e: any) => {
        setSelectedFromLocation(e)
        setTrs({
            ...trs,
            fromLocationID: e.value,
        })
    }
    const handleOnChangeToLocation = (e: any) => {
        setSelectedToLocation(e)
        setTrs({
            ...trs,
            toLocationID: e.value,
        })
    }
    const handleOnChangeDepartureBerth = (e: any) => {
        setTrs({ ...trs, departureBerth: e.target.value })
    }
    const handleOnChangeArrivalBerth = (e: any) => {
        setTrs({ ...trs, arrivalBerth: e.target.value })
    }
    const handleOnChangeStart = (e: any) => {
        setTrs({ ...trs, start: dayjs(e).format('YYYY-MM-DD') })
    }
    const handleOnChangeEnd = (e: any) => {
        setTrs({ ...trs, end: dayjs(e).format('YYYY-MM-DD') })
    }
    const handleOnChangeScheduleType = (e: any) => {
        setSelectedScheduleType(e)
        setTrs({ ...trs, scheduleType: e.value })
    }
    const handleOnChangeDaysOfWeek = (day: string, checked: boolean) => {
        let selection = [...selectedDaysOfWeek]
        if (checked) {
            selection = [...selectedDaysOfWeek, day]
        } else {
            selection = selection.filter((d: any) => d !== day)
        }
        setSelectedDaysOfWeek(selection)
        setTrs({
            ...trs,
            daysOfWeek: selection.join(','),
        })
    }

    const confirmDeleteSchedule = () => {
        setOpenConfirmDeleteDialog(true)
    }

    const [
        deleteTripReportSchedule,
        { loading: deleteTripReportScheduleLoading },
    ] = useMutation(DeleteTripReportSchedules, {
        onCompleted: (response: any) => {
            if (response.deleteTripReportSchedules) {
                // toast.success('Trip Report Schedule deleted successfully')
                router.push('/trip-report-schedules')
            } else {
                toast.error('Error deleting Trip Report Schedule')
            }
        },
        onError: (error: any) => {
            toast.error(`Error: ${error.message}`)
            console.error('deleteTripReportSchedule onError', error.message)
        },
    })

    const handleDeleteSchedule = async () => {
        await deleteTripReportSchedule({
            variables: {
                ids: [id],
            },
        })
    }
    const [
        readOneTripReportSchedule,
        { loading: readOneTripReportScheduleLoading },
    ] = useLazyQuery(ReadOneTripReportSchedule, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = { ...response.readOneTripReportSchedule }
            if (data) {
                const vehicles = data.vehicles.nodes.map((v: any) => v.id)
                setTrs({
                    ...data,
                    vehicles: vehicles.join(','),
                })
                setSelectedTripScheduleService({
                    value: data.tripScheduleService.id,
                    label: data.tripScheduleService.title,
                })
                setSelectedFromLocation({
                    value: data.fromLocation.id,
                    label: data.fromLocation.title,
                })
                setSelectedToLocation({
                    value: data.toLocation.id,
                    label: data.toLocation.title,
                })
                setSelectedScheduleType(
                    scheduleTypes.find(
                        (v: any) => v.value === data.scheduleType,
                    ),
                )
            }
        },
        onError: (error: any) => {
            console.error('readOneTripReportSchedule error', error)
        },
    })
    const [
        readTripScheduleServices,
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        { loading: _readTripScheduleServicesLoading },
    ] = useLazyQuery(ReadTripScheduleServices, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readTripScheduleServices.nodes
            if (data) {
                setTripScheduleServices(
                    data.map((v: any) => {
                        return { value: v.id, label: v.title }
                    }),
                )
            }
        },
        onError: (error: any) => {
            console.error('readTripScheduleServices error', error)
        },
    })
    const [
        readGeoLocations,
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        { loading: _readGeoLocationsLoading },
    ] = useLazyQuery(GET_GEO_LOCATIONS, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readGeoLocations.nodes
            if (data) {
                setGeoLocations(
                    data.map((v: any) => {
                        return { value: v.id, label: v.title }
                    }),
                )
            }
        },
        onError: (error: any) => {
            console.error('readGeoLocations error', error)
        },
    })
    const loadTripReportSchedule = async () => {
        await readOneTripReportSchedule({ variables: { id: id } })
    }
    const loadTripScheduleServices = async () => {
        await readTripScheduleServices()
    }
    const loadGeoLocations = async () => {
        await readGeoLocations()
    }
    const handleSetVessels = (e: any) => {
        const vesselList = e.map((vessel: any) => ({
            label: vessel.title,
            value: vessel.id,
        }))
        setVessels(vesselList)
    }

    const validateInputs = () => {
        let errorMessage = ''
        if (isEmpty(trim(trs.title))) {
            errorMessage += '\nThe title is required.'
        }
        if (isEmpty(selectedVessels)) {
            errorMessage += '\nAt least one vessel is required.'
        }

        if (!isEmpty(trim(errorMessage))) {
            toast.error(trim(errorMessage))
            return false
        } else {
            return true
        }
    }
    const [
        updateTripReportSchedule,
        { loading: updateTripReportScheduleLoading },
    ] = useMutation(UpdateTripReportSchedule, {
        onCompleted: (_response: any) => {
            router.push('/trip-report-schedules')
        },
        onError: (error: any) => {
            toast.error(error.message)
            console.error('updateTripReportSchedule onError', error.message)
        },
    })
    const saveTRS = async () => {
        const validated = validateInputs()
        if (!validated) return
        if (+id > 0) {
            const saveData = { ...trs }
            delete saveData.__typename
            delete saveData.fromLocation
            delete saveData.toLocation
            delete saveData.tripReportScheduleStops
            delete saveData.tripReport_LogBookEntrySections
            delete saveData.tripScheduleService
            // update
            await updateTripReportSchedule({
                variables: { input: saveData },
            })
        }
    }
    getVesselList(handleSetVessels)
    useEffect(() => {
        if (+id > 0) {
            loadTripReportSchedule()
        }
    }, [id])
    useEffect(() => {
        if (!isEmpty(vessels) && !isEmpty(trs.vehicles)) {
            const sv = trs.vehicles
                .split(',')
                .map((v: any) =>
                    vessels.find((vessel: any) => +vessel.value === +v),
                )
            setSelectedVessels(sv)
        }
    }, [vessels, trs])
    useEffect(() => {
        loadTripScheduleServices()
        loadGeoLocations()
    }, [])
    /* useEffect(() => {
        if (
            !isEmpty(tripReportSchedules) &&
            !isEmpty(trs.tripReportSchedules)
        ) {
            const sv = trs.tripReportSchedules
                .split(',')
                .map((item: any) =>
                    tripReportSchedules.find(
                        (trs: any) => +trs.value === +item,
                    ),
                )
            setSelectedTripReportSchedules(sv)
        }
    }, [tripReportSchedules, trs]) */
    return (
        <div className="w-full mb-20 md:mb-0">
            <div className="px-2 lg:px-4 mt-2 ">
                <div className="flex md:flex-nowrap md:flex-row gap-3 flex-col-reverse flex-wrap justify-between md:items-center items-start">
                    <H2>{+id > 0 ? 'Edit' : 'New'} Trip Report Schedule</H2>
                </div>
                <Separator className="my-4" />
                <div className="my-4">
                    <Label>Schedule Name</Label>
                    <Input
                        defaultValue={trs.title}
                        onChange={handleOnChangeTitle}
                        type="text"
                        placeholder="Title"
                    />
                </div>
                <div className="my-4">
                    <Label>Service</Label>
                    <Combobox
                        options={tripScheduleServices}
                        value={selectedTripScheduleService}
                        onChange={handleTripScheduleServiceChange}
                        placeholder="Select Trip Schedule Service"
                    />
                </div>
                <div className="my-4">
                    <Label>Transit Route ID</Label>
                    <Input
                        defaultValue={trs.transitRouteID}
                        onChange={handleOnChangeTransitRouteID}
                        type="text"
                        placeholder="Transit Route ID"
                    />
                </div>
                <div className="my-4">
                    <Label>Transit Trip ID</Label>
                    <Input
                        defaultValue={trs.transitTripID}
                        onChange={handleOnChangeTransitTripID}
                        type="text"
                        placeholder="Transit Route ID"
                    />
                </div>
                <div className="my-4">
                    <Label>Vessels</Label>
                    <Combobox
                        options={vessels}
                        value={selectedVessels}
                        onChange={handleVesselChange}
                        placeholder="Select Vessels"
                        multi={true}
                    />
                </div>
                <div className="my-4">
                    <Label>Depart Time</Label>
                    <TimeField
                        time={trs.departTime}
                        handleTimeChange={handleOnChangeDepartTime}
                        timeID="trs-depart-time"
                        fieldName="Depart Time"
                    />
                </div>
                <div className="my-4">
                    <Label>From</Label>
                    <Combobox
                        options={geoLocations}
                        value={selectedFromLocation}
                        onChange={handleOnChangeFromLocation}
                        placeholder="Select location"
                    />
                </div>
                <div className="my-4">
                    <Label>Departure Berth</Label>
                    <Input
                        defaultValue={trs.departureBerth}
                        onChange={handleOnChangeDepartureBerth}
                        type="text"
                        placeholder="Departure Berth"
                    />
                </div>
                <div className="my-4">
                    <Label>To</Label>
                    <Combobox
                        options={geoLocations}
                        value={selectedToLocation}
                        onChange={handleOnChangeToLocation}
                        placeholder="Select location"
                    />
                </div>
                <div className="my-4">
                    <Label>Arrival Berth</Label>
                    <Input
                        defaultValue={trs.arrivalBerth}
                        onChange={handleOnChangeArrivalBerth}
                        type="text"
                        placeholder="Arrival Berth"
                    />
                </div>
                <div className="my-4">
                    <Label>Arrival Time</Label>
                    <TimeField
                        time={trs.arriveTime}
                        handleTimeChange={handleOnChangeArriveTime}
                        timeID="trs-arrive-time"
                        fieldName="Arrive Time"
                    />
                </div>
                <div className="my-4">
                    <Label>Next Expected Contact Time</Label>
                    <TimeField
                        time={trs.expectedContactTime}
                        handleTimeChange={handleOnChangeExpectedContactTime}
                        timeID="trs-expected-contact-time"
                        fieldName="Expected Contact Time"
                    />
                </div>
                <Separator className="my-6" />
                <div>
                    <H4 className="mb-4">Schedule Occurence Details</H4>
                </div>
                <div className="my-4">
                    <Label>Start of Schedule Sailed</Label>
                    <DateField
                        dateID="trs-start-date"
                        fieldName="Start date"
                        date={trs.start}
                        handleDateChange={handleOnChangeStart}
                        hideButton={true}
                    />
                </div>
                <div className="my-4">
                    <Label>End of Schedule Sailed</Label>
                    <DateField
                        dateID="trs-end-date"
                        fieldName="End date"
                        date={trs.end}
                        handleDateChange={handleOnChangeEnd}
                        hideButton={true}
                    />
                </div>

                <Separator className="my-6" />

                <div className="my-4">
                    <Label>Schedule Occurs</Label>
                    <Combobox
                        options={scheduleTypes}
                        value={selectedScheduleType}
                        onChange={handleOnChangeScheduleType}
                        placeholder="Select Schedule Type"
                    />
                </div>
                {selectedScheduleType.value === 'WeekDays' &&
                    daysOfWeek.map((day, index) => (
                        <div
                            className={`flex items-center my-4 w-full`}
                            key={index}>
                            <Label
                                className={`relative flex items-center pr-3 rounded-full cursor-pointer`}
                                htmlFor={`day-of-week-${index}`}
                                data-ripple="true"
                                data-ripple-color="dark"
                                data-ripple-dark="true">
                                <Input
                                    type="checkbox"
                                    id={`day-of-week-${index}`}
                                    className="before:content[''] peer relative h-5 w-5 cursor-pointer p-3 appearance-none rounded-full border border-sky-400 transition-all before:absolute before:top-2/4 before:left-2/4 before:block before:h-12 before:w-12 before:-translate-y-2/4 before:-translate-x-2/4 before:rounded-full before:bg-blue-sky-500 before:opacity-0 before:transition-opacity checked:border-sky-700 checked:bg-sky-700 before:bg-sky-700 hover:before:opacity-10"
                                    checked={selectedDaysOfWeek.includes(
                                        day.value,
                                    )}
                                    onChange={(e: any) => {
                                        handleOnChangeDaysOfWeek(
                                            day.value,
                                            e.target.checked,
                                        )
                                    }}
                                />
                                <span className="absolute text-white transition-opacity opacity-0 pointer-events-none top-2/4 left-1/3 -translate-y-2/4 -translate-x-2/4 peer-checked:opacity-100"></span>
                                <span className="ml-3 text-sm font-semibold uppercase">
                                    {day.value}
                                </span>
                            </Label>
                        </div>
                    ))}

                <Separator className="my-6" />
                <div>
                    <H4 className="mb-4">Scheduled Stops</H4>
                    <TripReportScheduleStopList tripReportScheduleID={+id} />
                </div>

                <Separator className="my-6" />

                <div className="flex justify-end gap-2">
                    <Button
                        variant="text"
                        iconLeft={X}
                        onClick={() => {
                            router.push('/trip-report-schedules')
                        }}>
                        Cancel
                    </Button>
                    {+id > 0 && (
                        <Button
                            variant="destructive"
                            iconLeft={Trash2}
                            onClick={confirmDeleteSchedule}>
                            Delete
                        </Button>
                    )}
                    <Button
                        iconLeft={Save}
                        onClick={saveTRS}
                        isLoading={
                            readOneTripReportScheduleLoading ||
                            updateTripReportScheduleLoading
                        }>
                        {+id > 0 ? 'Update' : 'Save'} Changes
                    </Button>
                </div>
            </div>
            <AlertDialogNew
                openDialog={openConfirmDeleteDialog}
                setOpenDialog={setOpenConfirmDeleteDialog}
                handleCreate={handleDeleteSchedule}
                title="Delete Trip Report Schedule"
                description="Are you sure you want to delete this Trip Report Schedule?"
                actionText="Delete"
                variant="warning"
            />
        </div>
    )
}

export default TripReportScheduleForm
